// Clase principal del juego
class GameConsole {
    constructor() {
        // Elementos del DOM
        this.textArea = document.getElementById('text-area');
        this.inputEntry = document.getElementById('input-entry');
        this.sendButton = document.getElementById('send-button');

        // Estado del juego
        this.estadoJuego = 'menu_principal';
        this.nombrePersonaje = '';
        this.donSeleccionado = '';
        this.partidaGuardada = false;
        this.characterData = {};
        this.primeraVezPortales = true;

        // Configurar eventos
        this.sendButton.addEventListener('click', () => this.processInput());
        this.inputEntry.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                this.processInput();
            }
        });

        // Iniciar el juego
        this.mostrarMenuGui();
    }

    // Limpiar la consola
    clearConsole() {
        this.textArea.innerHTML = '';
    }

    // Imprimir mensaje en la consola con formato
    printToConsole(message, tag = null) {
        if (!message) return;

        const messageElement = document.createElement('div');
        messageElement.textContent = message;
        
        if (tag) {
            messageElement.classList.add(tag);
        } else {
            messageElement.classList.add('default');
        }

        this.textArea.appendChild(messageElement);
        this.textArea.scrollTop = this.textArea.scrollHeight; // Auto-scroll
    }

    // Mostrar menú principal
    mostrarMenuGui() {
        this.clearConsole();
        this.printToConsole('SOLO LEGENXS', 'title');
        this.printToConsole('\nMenú Principal:', 'info');
        
        if (this.partidaGuardada) {
            this.printToConsole('1. Continuar aventura', 'menu-option');
        } else {
            this.printToConsole('1. Comenzar una nueva aventura', 'menu-option');
        }
        
        this.printToConsole('2. Ajustes (Próximamente)', 'menu-option');
        this.printToConsole('3. Salir', 'menu-option');
        this.printToConsole('\nEscribe el número de tu elección:', 'prompt');
        
        this.estadoJuego = 'menu_principal';
    }

    // Mostrar opciones de don
    mostrarOpcionesDon() {
        this.clearConsole();
        this.printToConsole('Elige un Don para tu Personaje:', 'title');
        this.printToConsole('Los Dones otorgan mejoras pasivas y pueden influir en tus decisiones.', 'info');
        this.printToConsole('1. Resistencia', 'menu-option');
        this.printToConsole('2. Velocidad', 'menu-option');
        this.printToConsole('3. Magia', 'menu-option');
        this.printToConsole('4. Ver descripción de los Dones (D)', 'menu-option');
        this.printToConsole('\nEscribe el número o letra de tu elección:', 'prompt');
        
        this.estadoJuego = 'seleccion_don';
    }

    // Mostrar descripción de los dones
    mostrarDescripcionDones() {
        this.clearConsole();
        this.printToConsole('Descripción de los Dones:', 'title');
        this.printToConsole('Cada Don ofrece una bonificación pasiva única que afecta tus atributos al subir de nivel y puede abrir opciones especiales en ciertos eventos del juego.', 'info');
        this.printToConsole('\n- Resistencia: Aumenta tu capacidad de supervivencia, otorgando más puntos de vida o reducción de daño.', 'story');
        this.printToConsole('- Velocidad: Mejora tu agilidad, permitiéndote actuar antes en combate o tener éxito en pruebas de destreza.', 'story');
        this.printToConsole('- Magia: Potencia tus habilidades arcanas, incrementando el daño de hechizos o desbloqueando conocimientos ocultos.', 'story');
        this.printToConsole('\nPresiona Enter para volver a la selección de Dones.', 'prompt');
        
        this.estadoJuego = 'descripcion_don';
    }

    // Mostrar menú del pueblo
    mostrarMenuPuebloGui() {
        this.clearConsole();
        this.printToConsole(`El Pueblo - ${this.nombrePersonaje}`, 'title');
        this.printToConsole('\n¿Qué deseas hacer?', 'info');
        this.printToConsole('1. Ir de raid', 'menu-option');
        this.printToConsole('2. Abrir inventario (Próximamente)', 'menu-option');
        this.printToConsole('3. Personaje (Próximamente)', 'menu-option');
        this.printToConsole('4. Tiendas (Próximamente)', 'menu-option');
        this.printToConsole('5. Ajustes (Próximamente)', 'menu-option');
        this.printToConsole('6. Guardar partida', 'menu-option');
        this.printToConsole('7. Salir al menú principal', 'menu-option');
        this.printToConsole('\nEscribe el número de tu elección:', 'prompt');
        
        this.estadoJuego = 'pueblo';
    }

    // Mostrar menú de portales mágicos
    mostrarMenuPortalesGui(mostrarIntro = false) {
        this.clearConsole();
        
        if (mostrarIntro) {
            const lineasIntroPortales = [
                'Te encuentras ante un claro ancestral, la energía mágica es palpable.',
                'Frente a ti se alzan varios portales de distintos tamaños y colores.',
                'Estos son \'Los Portales Mágicos\', conductos a otras dimensiones y realidades.',
                'Se dice que aventurarse en ellos puede llevar a grandes tesoros... o a un peligro inmenso.',
                'Los monstruos que habitan al otro lado custodian materiales valiosos para mejorar tu equipo.'
            ];
            
            this.printToConsole('--- Los Portales Mágicos ---', 'title');
            
            // Mostrar las líneas de introducción con un efecto de escritura
            this.mostrarTextoSecuencial(lineasIntroPortales, 0, () => {
                this.printToConsole('\nPresiona Enter para continuar...', 'prompt');
                this.estadoJuego = 'portales_intro_espera';
            });
            
            return;
        }
        
        this.printToConsole('--- Los Portales Mágicos ---', 'title');
        this.printToConsole('1. Entrar en el portal principal', 'menu-option');
        this.printToConsole('2. Inventario', 'menu-option');
        this.printToConsole('3. Volver a El Pueblo', 'menu-option');
        this.printToConsole('4. Leer historia de los portales (D)', 'menu-option');
        this.printToConsole('---------------------------', 'info');
        this.printToConsole('Elige una opción:', 'prompt');
        
        this.estadoJuego = 'portales_magicos';
    }

    // Mostrar texto secuencial con efecto de escritura
    mostrarTextoSecuencial(lineas, indice, callback) {
        if (indice >= lineas.length) {
            if (callback) callback();
            return;
        }
        
        this.printToConsole(lineas[indice], 'story');
        
        setTimeout(() => {
            this.mostrarTextoSecuencial(lineas, indice + 1, callback);
        }, 1000);
    }

    // Solicitar nombre del personaje
    solicitarNombrePersonaje() {
        this.printToConsole('A continuación, elige un nombre para tu héroe.', 'prompt');
        
        // En la versión web usamos un prompt en lugar de un diálogo personalizado
        const nombre = prompt('Introduce el nombre de tu personaje:');
        
        if (nombre) {
            this.nombrePersonaje = nombre;
            this.characterData.nombre = this.nombrePersonaje;
            this.printToConsole(`Tu personaje se llama: ${this.nombrePersonaje}`, 'success');
            
            setTimeout(() => this.mostrarOpcionesDon(), 1000);
        } else {
            this.printToConsole('Creación de personaje cancelada. Volviendo al menú principal.', 'info');
            
            setTimeout(() => this.mostrarMenuGui(), 2000);
        }
    }

    // Procesar la entrada del usuario
    processInput() {
        const userInput = this.inputEntry.value.trim();
        const opcion = userInput.toLowerCase();
        
        this.inputEntry.value = ''; // Limpiar el campo de entrada
        this.printToConsole(`> ${userInput}`, 'user-input');
        
        switch (this.estadoJuego) {
            case 'menu_principal':
                this.procesarMenuPrincipal(opcion);
                break;
            case 'seleccion_don':
                this.procesarSeleccionDon(opcion);
                break;
            case 'descripcion_don':
                this.mostrarOpcionesDon();
                break;
            case 'pueblo':
                this.procesarPueblo(opcion);
                break;
            case 'portales_intro_espera':
                if (this.primeraVezPortales) {
                    this.primeraVezPortales = false;
                }
                this.mostrarMenuPortalesGui(false);
                break;
            case 'portales_magicos':
                this.procesarPortalesMagicos(opcion);
                break;
        }
    }

    // Procesar opciones del menú principal
    procesarMenuPrincipal(opcion) {
        if (opcion === '1') {
            if (this.partidaGuardada) {
                this.clearConsole();
                this.printToConsole('Continuando aventura... (Próximamente)', 'info');
                
                setTimeout(() => this.mostrarMenuPuebloGui(), 2000);
            } else {
                this.estadoJuego = 'nueva_partida_intro';
                this.clearConsole();
                this.printToConsole('Iniciando nueva aventura...', 'info');
                
                const lineasIntro = [
                    'Bienvenido al juego de las leyendas,',
                    'este juego será recordado por su dificultad',
                    'y por ser tremendamente adictivo,',
                    'sin más dilación continuemos...'
                ];
                
                this.mostrarTextoSecuencial(lineasIntro, 0, () => {
                    setTimeout(() => this.solicitarNombrePersonaje(), 1000);
                });
            }
        } else if (opcion === '2') {
            this.clearConsole();
            this.printToConsole('Ajustes del juego (Próximamente)...', 'info');
            
            setTimeout(() => this.mostrarMenuGui(), 2000);
        } else if (opcion === '3') {
            if (confirm('¿Estás seguro de que quieres salir del juego?')) {
                this.clearConsole();
                this.printToConsole('Gracias por jugar a Solo Legenxs. ¡Hasta pronto!', 'info');
                
                // En la versión web no podemos cerrar la ventana automáticamente
                // por restricciones de seguridad del navegador
                this.printToConsole('Puedes cerrar esta ventana o recargar la página para volver a jugar.', 'info');
            } else {
                this.mostrarMenuGui();
            }
        } else {
            this.printToConsole('Opción no válida. Inténtalo de nuevo.', 'error');
            
            setTimeout(() => this.mostrarMenuGui(), 1500);
        }
    }

    // Procesar opciones de selección de don
    procesarSeleccionDon(opcion) {
        this.donSeleccionado = '';
        
        if (opcion === '1' || opcion === 'resistencia') {
            this.donSeleccionado = 'Resistencia';
        } else if (opcion === '2' || opcion === 'velocidad') {
            this.donSeleccionado = 'Velocidad';
        } else if (opcion === '3' || opcion === 'magia') {
            this.donSeleccionado = 'Magia';
        } else if (opcion === '4' || opcion === 'd') {
            this.mostrarDescripcionDones();
            return;
        } else {
            this.printToConsole('Opción no válida. Elige un Don de la lista o \'D\' para descripción.', 'error');
            
            setTimeout(() => this.mostrarOpcionesDon(), 2000);
            return;
        }
        
        if (this.donSeleccionado) {
            this.characterData.don = this.donSeleccionado;
            this.printToConsole(`Has elegido el Don de ${this.donSeleccionado}. ¡Sabia elección!`, 'success');
            this.partidaGuardada = true;
            this.printToConsole('Personaje creado y Don seleccionado.', 'info');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 2000);
        }
    }

    // Procesar opciones del pueblo
    procesarPueblo(opcion) {
        if (opcion === '1') {
            this.estadoJuego = 'portales_magicos';
            this.mostrarMenuPortalesGui(mostrarIntro = this.primeraVezPortales);
        } else if (opcion === '2') {
            this.printToConsole('Funcionalidad \'Abrir inventario\' próximamente...', 'info');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 1500);
        } else if (opcion === '3') {
            this.printToConsole('Funcionalidad \'Personaje\' próximamente...', 'info');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 1500);
        } else if (opcion === '4') {
            this.printToConsole('Funcionalidad \'Tiendas\' próximamente...', 'info');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 1500);
        } else if (opcion === '5') {
            this.printToConsole('Funcionalidad \'Ajustes\' próximamente...', 'info');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 1500);
        } else if (opcion === '6') {
            this.partidaGuardada = true;
            this.printToConsole('Partida guardada exitosamente.', 'success');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 1500);
        } else if (opcion === '7') {
            this.printToConsole('Volviendo al menú principal...', 'info');
            
            setTimeout(() => this.mostrarMenuGui(), 1500);
        } else {
            this.printToConsole('Opción no válida. Inténtalo de nuevo.', 'error');
            
            setTimeout(() => this.mostrarMenuPuebloGui(), 1500);
        }
    }

    // Procesar opciones de los portales mágicos
    procesarPortalesMagicos(opcion) {
        if (opcion === '1') {
            this.printToConsole('Funcionalidad \'Entrar en el portal principal\' próximamente...', 'info');
            
            setTimeout(() => this.mostrarMenuPortalesGui(false), 1500);
        } else if (opcion === '2') {
            this.printToConsole('Funcionalidad \'Abrir inventario\' (desde portales) próximamente...', 'info');
            
            setTimeout(() => this.mostrarMenuPortalesGui(false), 1500);
        } else if (opcion === '3') {
            this.mostrarMenuPuebloGui();
        } else if (opcion === '4' || opcion === 'd') {
            this.mostrarMenuPortalesGui(true);
        } else {
            this.printToConsole('Opción no válida. Inténtalo de nuevo.', 'error');
            
            setTimeout(() => this.mostrarMenuPortalesGui(false), 1500);
        }
    }
}

// Iniciar el juego cuando se cargue la página
document.addEventListener('DOMContentLoaded', () => {
    const game = new GameConsole();
});