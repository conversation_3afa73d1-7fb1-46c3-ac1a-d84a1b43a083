// Clase principal del juego - Versión moderna
class SoloLegenxsGame {
    constructor() {
        // Elementos del DOM
        this.storyContent = document.getElementById('story-content');
        this.optionsContainer = document.getElementById('options-container');
        this.inputContainer = document.getElementById('input-container');
        this.textInput = document.getElementById('text-input');
        this.submitBtn = document.getElementById('submit-btn');
        this.playerInfo = document.getElementById('player-info');

        // Estado del juego
        this.gameState = 'main_menu';
        this.playerData = {
            name: '',
            gift: '',
            level: 1,
            savedGame: false
        };
        this.firstTimePortals = true;

        // Configurar eventos
        this.setupEventListeners();

        // Inicializar el juego
        this.init();
    }

    setupEventListeners() {
        this.submitBtn.addEventListener('click', () => this.handleTextInput());
        this.textInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                this.handleTextInput();
            }
        });
    }

    init() {
        this.showMainMenu();
        this.updatePlayerInfo();
    }

    // Métodos de utilidad para la interfaz
    clearStory() {
        this.storyContent.innerHTML = '';
    }

    clearOptions() {
        this.optionsContainer.innerHTML = '';
    }

    showTextInput(placeholder = 'Escribe aquí...') {
        this.inputContainer.style.display = 'flex';
        this.optionsContainer.style.display = 'none';
        this.textInput.placeholder = placeholder;
        this.textInput.focus();
    }

    showOptions() {
        this.inputContainer.style.display = 'none';
        this.optionsContainer.style.display = 'grid';
    }

    hideInterface() {
        this.inputContainer.style.display = 'none';
        this.optionsContainer.style.display = 'none';
    }

    // Agregar texto a la historia
    addStoryText(text, className = 'text-info', animate = true) {
        const element = document.createElement('div');
        element.className = className;
        element.textContent = text;

        if (animate) {
            element.classList.add('fade-in');
        }

        this.storyContent.appendChild(element);
        this.storyContent.scrollTop = this.storyContent.scrollHeight;
    }

    // Crear botón de opción
    createOptionButton(text, action) {
        const button = document.createElement('button');
        button.className = 'option-btn slide-in';
        button.textContent = text;
        button.addEventListener('click', () => {
            this.addStoryText(`> ${text}`, 'text-user-input');
            action();
        });
        return button;
    }

    // Mostrar opciones
    showOptionsMenu(options) {
        this.clearOptions();
        options.forEach((option, index) => {
            const button = this.createOptionButton(option.text, option.action);
            // Añadir delay para animación escalonada
            setTimeout(() => {
                this.optionsContainer.appendChild(button);
            }, index * 100);
        });
        this.showOptions();
    }

    // Actualizar información del jugador
    updatePlayerInfo() {
        if (this.playerData.name) {
            this.playerInfo.textContent = `${this.playerData.name} | Don: ${this.playerData.gift} | Nivel: ${this.playerData.level}`;
        } else {
            this.playerInfo.textContent = 'Bienvenido a Solo Legenxs';
        }
    }

    // Efecto de escritura secuencial
    async typeText(texts, delay = 1000) {
        this.hideInterface();

        for (const text of texts) {
            this.addStoryText(text.content, text.className || 'text-story');
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // Manejar entrada de texto
    handleTextInput() {
        const input = this.textInput.value.trim();
        if (!input) return;

        this.addStoryText(`> ${input}`, 'text-user-input');
        this.textInput.value = '';

        // Procesar según el estado del juego
        switch (this.gameState) {
            case 'enter_name':
                this.processPlayerName(input);
                break;
            default:
                this.addStoryText('Comando no reconocido.', 'text-error');
        }
    }

    // Procesar nombre del jugador
    processPlayerName(name) {
        if (name.length < 2) {
            this.addStoryText('El nombre debe tener al menos 2 caracteres.', 'text-error');
            return;
        }

        this.playerData.name = name;
        this.addStoryText(`¡Perfecto! Tu héroe se llamará ${name}.`, 'text-success');
        this.updatePlayerInfo();

        setTimeout(() => {
            this.showGiftSelection();
        }, 1500);
    }

    // Mostrar menú principal
    showMainMenu() {
        this.clearStory();
        this.gameState = 'main_menu';

        this.addStoryText('Menú Principal', 'text-title');
        this.addStoryText('¿Qué deseas hacer?', 'text-info');

        const options = [];

        if (this.playerData.savedGame) {
            options.push({
                text: '🎮 Continuar Aventura',
                action: () => this.showTownMenu()
            });
        } else {
            options.push({
                text: '⚔️ Nueva Aventura',
                action: () => this.startNewGame()
            });
        }

        options.push(
            {
                text: '⚙️ Ajustes',
                action: () => this.showSettings()
            },
            {
                text: '🚪 Salir',
                action: () => this.exitGame()
            }
        );

        this.showOptionsMenu(options);
    }

    // Iniciar nueva partida
    async startNewGame() {
        this.clearStory();
        this.gameState = 'new_game';

        const introTexts = [
            { content: 'Iniciando nueva aventura...', className: 'text-info' },
            { content: 'Bienvenido al mundo de las leyendas,', className: 'text-story' },
            { content: 'donde cada decisión forja tu destino', className: 'text-story' },
            { content: 'y cada batalla te acerca a la gloria.', className: 'text-story' },
            { content: 'Prepárate para una experiencia épica...', className: 'text-story' }
        ];

        await this.typeText(introTexts, 1200);

        setTimeout(() => {
            this.requestPlayerName();
        }, 1000);
    }

    // Solicitar nombre del jugador
    requestPlayerName() {
        this.clearStory();
        this.gameState = 'enter_name';

        this.addStoryText('Creación de Personaje', 'text-title');
        this.addStoryText('Antes de comenzar tu aventura, necesitas un nombre que inspire respeto y temor en tus enemigos.', 'text-info');
        this.addStoryText('¿Cómo se llamará tu héroe?', 'text-info');

        this.showTextInput('Introduce el nombre de tu héroe...');
    }

    // Mostrar selección de don
    showGiftSelection() {
        this.clearStory();
        this.gameState = 'gift_selection';

        this.addStoryText('Selección de Don', 'text-title');
        this.addStoryText('Los antiguos dioses te han bendecido con la oportunidad de elegir un don especial.', 'text-info');
        this.addStoryText('Este don te acompañará durante toda tu aventura y definirá tu estilo de combate.', 'text-info');

        const options = [
            {
                text: '🛡️ Don de Resistencia',
                action: () => this.selectGift('Resistencia')
            },
            {
                text: '⚡ Don de Velocidad',
                action: () => this.selectGift('Velocidad')
            },
            {
                text: '🔮 Don de Magia',
                action: () => this.selectGift('Magia')
            },
            {
                text: '📖 Ver Descripciones',
                action: () => this.showGiftDescriptions()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Mostrar descripciones de los dones
    showGiftDescriptions() {
        this.clearStory();
        this.gameState = 'gift_descriptions';

        this.addStoryText('Descripciones de los Dones', 'text-title');
        this.addStoryText('Cada don otorga habilidades únicas que influirán en tu aventura:', 'text-info');

        this.addStoryText('🛡️ Don de Resistencia:', 'text-warning');
        this.addStoryText('Aumenta tu resistencia física y mental. Los héroes con este don pueden soportar más daño y resistir efectos negativos. Ideal para guerreros que prefieren el combate directo.', 'text-story');

        this.addStoryText('⚡ Don de Velocidad:', 'text-warning');
        this.addStoryText('Mejora tu agilidad y reflejos. Permite esquivar ataques más fácilmente y actuar primero en combate. Perfecto para exploradores y ladrones.', 'text-story');

        this.addStoryText('🔮 Don de Magia:', 'text-warning');
        this.addStoryText('Desbloquea el poder arcano que fluye por el mundo. Permite lanzar hechizos poderosos y comprender secretos místicos. Esencial para magos y eruditos.', 'text-story');

        const options = [
            {
                text: '↩️ Volver a la Selección',
                action: () => this.showGiftSelection()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Seleccionar don
    selectGift(gift) {
        this.playerData.gift = gift;
        this.playerData.savedGame = true;

        this.clearStory();
        this.addStoryText(`¡Has elegido el Don de ${gift}!`, 'text-success');
        this.addStoryText(`Los dioses sonríen ante tu elección. El poder del ${gift} fluye ahora por tus venas.`, 'text-story');
        this.addStoryText('Tu personaje ha sido creado exitosamente.', 'text-info');

        this.updatePlayerInfo();

        setTimeout(() => {
            this.showTownMenu();
        }, 2500);
    }

    // Mostrar menú del pueblo
    showTownMenu() {
        this.clearStory();
        this.gameState = 'town_menu';

        this.addStoryText(`El Pueblo - ${this.playerData.name}`, 'text-title');
        this.addStoryText('Te encuentras en el corazón del pueblo, donde aventureros de todo el reino se reúnen para planear sus próximas hazañas.', 'text-info');
        this.addStoryText('¿Qué deseas hacer?', 'text-info');

        const options = [
            {
                text: '⚔️ Ir de Raid',
                action: () => this.showPortalsMenu()
            },
            {
                text: '🎒 Inventario',
                action: () => this.showInventory()
            },
            {
                text: '👤 Personaje',
                action: () => this.showCharacterInfo()
            },
            {
                text: '🏪 Tiendas',
                action: () => this.showShops()
            },
            {
                text: '💾 Guardar Partida',
                action: () => this.saveGame()
            },
            {
                text: '🏠 Menú Principal',
                action: () => this.showMainMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Mostrar menú de portales
    async showPortalsMenu() {
        this.clearStory();
        this.gameState = 'portals_menu';

        if (this.firstTimePortals) {
            const introTexts = [
                { content: 'Los Portales Mágicos', className: 'text-title' },
                { content: 'Te encuentras ante un claro ancestral, donde la energía mágica es palpable en el aire.', className: 'text-story' },
                { content: 'Frente a ti se alzan varios portales de distintos tamaños y colores, pulsando con poder arcano.', className: 'text-story' },
                { content: 'Estos son los legendarios Portales Mágicos, conductos a otras dimensiones y realidades.', className: 'text-story' },
                { content: 'Se dice que aventurarse en ellos puede llevar a grandes tesoros... o a un peligro inmenso.', className: 'text-story' },
                { content: 'Al derrotar a los monstruos, sus cuerpos sueltan materiales valiosos que los humanos pueden usar para vender a los comerciantes o forjar piezas de equipamiento que van desde usuales hasta impresionantes piezas legendarias con poderes impensables.', className: 'text-story' }
            ];

            await this.typeText(introTexts, 1500);
            this.firstTimePortals = false;
        } else {
            this.addStoryText('Los Portales Mágicos', 'text-title');
            this.addStoryText('Los portales brillan con energía mística, esperando tu decisión.', 'text-info');
        }

        const options = [
            {
                text: '🌀 Entrar al Portal Principal',
                action: () => this.enterPortal()
            },
            {
                text: '🎒 Revisar Inventario',
                action: () => this.showInventory()
            },
            {
                text: '🏘️ Volver al Pueblo',
                action: () => this.showTownMenu()
            },
            {
                text: '📜 Leer Historia de los Portales',
                action: () => this.showPortalHistory()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Métodos de funcionalidades (próximamente)
    showInventory() {
        this.clearStory();
        this.addStoryText('Inventario', 'text-title');
        this.addStoryText('Tu inventario está vacío por ahora. Esta funcionalidad estará disponible próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showTownMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showCharacterInfo() {
        this.clearStory();
        this.addStoryText('Información del Personaje', 'text-title');
        this.addStoryText(`Nombre: ${this.playerData.name}`, 'text-info');
        this.addStoryText(`Don: ${this.playerData.gift}`, 'text-info');
        this.addStoryText(`Nivel: ${this.playerData.level}`, 'text-info');
        this.addStoryText('Más estadísticas estarán disponibles próximamente.', 'text-story');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showTownMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showShops() {
        this.clearStory();
        this.addStoryText('Tiendas', 'text-title');
        this.addStoryText('Las tiendas del pueblo estarán disponibles próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showTownMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showSettings() {
        this.clearStory();
        this.addStoryText('Ajustes', 'text-title');
        this.addStoryText('Los ajustes del juego estarán disponibles próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showMainMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    enterPortal() {
        this.clearStory();
        this.addStoryText('Entrando al Portal...', 'text-title');
        this.addStoryText('El sistema de combate y exploración estará disponible próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    async showPortalHistory() {
        this.clearStory();
        this.gameState = 'portal_history';

        const historyTexts = [
            { content: 'Historia de los Portales Mágicos', className: 'text-title' },
            { content: 'Te encuentras ante un claro ancestral, donde la energía mágica es palpable en el aire.', className: 'text-story' },
            { content: 'Frente a ti se alzan varios portales de distintos tamaños y colores, pulsando con poder arcano.', className: 'text-story' },
            { content: 'Estos son los legendarios Portales Mágicos, conductos a otras dimensiones y realidades.', className: 'text-story' },
            { content: 'Se dice que aventurarse en ellos puede llevar a grandes tesoros... o a un peligro inmenso.', className: 'text-story' },
            { content: 'Al derrotar a los monstruos, sus cuerpos sueltan materiales valiosos que los humanos pueden usar para vender a los comerciantes o forjar piezas de equipamiento que van desde usuales hasta impresionantes piezas legendarias con poderes impensables.', className: 'text-story' }
        ];

        await this.typeText(historyTexts, 1500);

        const options = [
            {
                text: '↩️ Volver a los Portales',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    saveGame() {
        this.playerData.savedGame = true;
        this.addStoryText('¡Partida guardada exitosamente!', 'text-success');

        setTimeout(() => {
            this.showTownMenu();
        }, 1500);
    }

    exitGame() {
        if (confirm('¿Estás seguro de que quieres salir del juego?')) {
            this.clearStory();
            this.addStoryText('¡Gracias por jugar Solo Legenxs!', 'text-success');
            this.addStoryText('Tu aventura te espera cuando regreses.', 'text-story');
            this.hideInterface();
        } else {
            this.showMainMenu();
        }
    }
}

// Inicializar el juego cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    const game = new SoloLegenxsGame();
});