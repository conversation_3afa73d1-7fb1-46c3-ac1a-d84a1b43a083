// Clase principal del juego - Versión moderna
class SoloLegenxsGame {
    constructor() {
        // Elementos del DOM
        this.storyContent = document.getElementById('story-content');
        this.optionsContainer = document.getElementById('options-container');
        this.inputContainer = document.getElementById('input-container');
        this.textInput = document.getElementById('text-input');
        this.submitBtn = document.getElementById('submit-btn');
        this.playerInfo = document.getElementById('player-info');

        // Estado del juego
        this.gameState = 'main_menu';
        this.playerData = {
            name: '',
            gift: '',
            level: 1,
            hp: 100,
            maxHp: 100,
            attack: 20,
            defense: 10,
            savedGame: false,
            inventory: []
        };
        this.firstTimePortals = true;
        this.currentEnemy = null;
        this.currentPortal = null;

        // Datos de monstruos y objetos
        this.initializeGameData();

        // Configurar eventos
        this.setupEventListeners();

        // Inicializar el juego
        this.init();
    }

    setupEventListeners() {
        this.submitBtn.addEventListener('click', () => this.handleTextInput());
        this.textInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                this.handleTextInput();
            }
        });
    }

    init() {
        this.showMainMenu();
        this.updatePlayerInfo();
    }

    initializeGameData() {
        // Rarezas de objetos
        this.rarities = {
            common: { name: 'Común', color: 'text-info', chance: 50 },
            uncommon: { name: 'Poco Común', color: 'text-warning', chance: 30 },
            rare: { name: 'Raro', color: 'text-success', chance: 15 },
            epic: { name: 'Épico', color: 'text-error', chance: 4 },
            legendary: { name: 'Legendario', color: 'text-title', chance: 1 }
        };

        // Monstruos del Portal Verde
        this.portalMonsters = {
            verde: [
                {
                    name: 'Lobo Sombrío',
                    hp: 80,
                    maxHp: 80,
                    attack: 15,
                    defense: 5,
                    description: 'Un lobo de pelaje negro como la noche, con ojos que brillan con malicia.',
                    loot: [
                        { name: 'Colmillo de Lobo', rarity: 'common', description: 'Un colmillo afilado de lobo sombrío.' },
                        { name: 'Piel de Lobo Sombría', rarity: 'uncommon', description: 'Piel oscura que absorbe la luz.' },
                        { name: 'Garra Sombría', rarity: 'rare', description: 'Una garra imbuida con energía oscura.' },
                        { name: 'Corazón de Sombras', rarity: 'epic', description: 'El corazón palpitante de un lobo sombrío.' },
                        { name: 'Esencia Lupina Legendaria', rarity: 'legendary', description: 'La esencia pura del espíritu del lobo.' }
                    ]
                },
                {
                    name: 'Araña Gigante',
                    hp: 60,
                    maxHp: 60,
                    attack: 18,
                    defense: 3,
                    description: 'Una araña del tamaño de un caballo con quelíceros venenosos.',
                    loot: [
                        { name: 'Seda de Araña', rarity: 'common', description: 'Hilo resistente de araña gigante.' },
                        { name: 'Veneno de Araña', rarity: 'uncommon', description: 'Veneno extraído de los quelíceros.' },
                        { name: 'Quelícero Afilado', rarity: 'rare', description: 'Mandíbula venenosa de araña gigante.' },
                        { name: 'Glándula Venenosa', rarity: 'epic', description: 'Órgano que produce veneno letal.' },
                        { name: 'Núcleo Arácnido Ancestral', rarity: 'legendary', description: 'El núcleo de poder de una araña milenaria.' }
                    ]
                },
                {
                    name: 'Goblin Salvaje',
                    hp: 50,
                    maxHp: 50,
                    attack: 12,
                    defense: 8,
                    description: 'Un goblin feroz con garras afiladas y dientes puntiagudos.',
                    loot: [
                        { name: 'Diente de Goblin', rarity: 'common', description: 'Un diente afilado de goblin salvaje.' },
                        { name: 'Oreja de Goblin', rarity: 'uncommon', description: 'Oreja puntiaguda con propiedades mágicas.' },
                        { name: 'Garra Goblin', rarity: 'rare', description: 'Garra afilada como navaja.' },
                        { name: 'Cerebro de Goblin', rarity: 'epic', description: 'Cerebro que contiene astucia primitiva.' },
                        { name: 'Alma Goblin Primordial', rarity: 'legendary', description: 'El alma salvaje de un goblin ancestral.' }
                    ]
                },
                {
                    name: 'Ent Corrupto',
                    hp: 120,
                    maxHp: 120,
                    attack: 25,
                    defense: 15,
                    description: 'Un árbol ancestral corrompido por la magia oscura.',
                    loot: [
                        { name: 'Corteza Corrupta', rarity: 'common', description: 'Corteza endurecida por la corrupción.' },
                        { name: 'Savia Negra', rarity: 'uncommon', description: 'Savia espesa y oscura del ent.' },
                        { name: 'Rama del Dolor', rarity: 'rare', description: 'Rama que susurra lamentos.' },
                        { name: 'Corazón de Madera Maldita', rarity: 'epic', description: 'El núcleo corrompido del ent.' },
                        { name: 'Semilla del Árbol Eterno', rarity: 'legendary', description: 'Semilla que contiene milenios de sabiduría.' }
                    ]
                },
                {
                    name: 'Oso Espectral',
                    hp: 100,
                    maxHp: 100,
                    attack: 22,
                    defense: 12,
                    description: 'Un oso fantasmal que vaga por el bosque en busca de venganza.',
                    loot: [
                        { name: 'Pelo Espectral', rarity: 'common', description: 'Pelo que brilla con luz fantasmal.' },
                        { name: 'Garra Fantasma', rarity: 'uncommon', description: 'Garra que puede tocar lo intangible.' },
                        { name: 'Colmillo Etéreo', rarity: 'rare', description: 'Colmillo que existe entre dimensiones.' },
                        { name: 'Corazón Espectral', rarity: 'epic', description: 'Corazón que late con energía fantasmal.' },
                        { name: 'Esencia del Más Allá', rarity: 'legendary', description: 'Fragmento puro del reino espectral.' }
                    ]
                },
                {
                    name: 'Ciervo de Cristal',
                    hp: 70,
                    maxHp: 70,
                    attack: 16,
                    defense: 20,
                    description: 'Un ciervo con cuernos de cristal que reflejan la luz mágica.',
                    loot: [
                        { name: 'Fragmento de Cristal', rarity: 'common', description: 'Pequeño fragmento de cuerno cristalino.' },
                        { name: 'Pelo Cristalino', rarity: 'uncommon', description: 'Pelo que tintinea como campanas.' },
                        { name: 'Cuerno de Cristal', rarity: 'rare', description: 'Cuerno completo de cristal mágico.' },
                        { name: 'Lágrima de Cristal', rarity: 'epic', description: 'Lágrima solidificada del ciervo.' },
                        { name: 'Núcleo de Pureza Eterna', rarity: 'legendary', description: 'El núcleo de pureza absoluta.' }
                    ]
                },
                {
                    name: 'Hongo Venenoso Gigante',
                    hp: 90,
                    maxHp: 90,
                    attack: 14,
                    defense: 18,
                    description: 'Un hongo colosal que libera esporas tóxicas.',
                    loot: [
                        { name: 'Espora Común', rarity: 'common', description: 'Espora básica del hongo gigante.' },
                        { name: 'Tallo Venenoso', rarity: 'uncommon', description: 'Tallo que gotea veneno.' },
                        { name: 'Sombrero Tóxico', rarity: 'rare', description: 'Sombrero cargado de toxinas.' },
                        { name: 'Núcleo Fúngico', rarity: 'epic', description: 'El centro vital del hongo.' },
                        { name: 'Esencia Micológica Primordial', rarity: 'legendary', description: 'La esencia del primer hongo.' }
                    ]
                },
                {
                    name: 'Serpiente de Jade',
                    hp: 65,
                    maxHp: 65,
                    attack: 20,
                    defense: 7,
                    description: 'Una serpiente con escamas verdes como el jade y veneno mortal.',
                    loot: [
                        { name: 'Escama de Jade', rarity: 'common', description: 'Escama brillante como una gema.' },
                        { name: 'Veneno de Jade', rarity: 'uncommon', description: 'Veneno verde esmeralda.' },
                        { name: 'Colmillo de Jade', rarity: 'rare', description: 'Colmillo venenoso cristalizado.' },
                        { name: 'Corazón de Serpiente', rarity: 'epic', description: 'Corazón que late con poder reptil.' },
                        { name: 'Alma Serpentina Ancestral', rarity: 'legendary', description: 'El alma de la primera serpiente.' }
                    ]
                }
            ]
        };
    }

    // Métodos de utilidad para la interfaz
    clearStory() {
        this.storyContent.innerHTML = '';
    }

    clearOptions() {
        this.optionsContainer.innerHTML = '';
    }

    showTextInput(placeholder = 'Escribe aquí...') {
        this.inputContainer.style.display = 'flex';
        this.optionsContainer.style.display = 'none';
        this.textInput.placeholder = placeholder;
        this.textInput.focus();
    }

    showOptions() {
        this.inputContainer.style.display = 'none';
        this.optionsContainer.style.display = 'grid';
    }

    hideInterface() {
        this.inputContainer.style.display = 'none';
        this.optionsContainer.style.display = 'none';
    }

    // Agregar texto a la historia
    addStoryText(text, className = 'text-info', animate = true) {
        const element = document.createElement('div');
        element.className = className;
        element.textContent = text;

        if (animate) {
            element.classList.add('fade-in');
        }

        this.storyContent.appendChild(element);
        this.storyContent.scrollTop = this.storyContent.scrollHeight;
    }

    // Crear botón de opción
    createOptionButton(text, action) {
        const button = document.createElement('button');
        button.className = 'option-btn slide-in';
        button.textContent = text;
        button.addEventListener('click', () => {
            this.addStoryText(`> ${text}`, 'text-user-input');
            action();
        });
        return button;
    }

    // Mostrar opciones
    showOptionsMenu(options) {
        this.clearOptions();
        options.forEach((option, index) => {
            const button = this.createOptionButton(option.text, option.action);
            // Añadir delay para animación escalonada
            setTimeout(() => {
                this.optionsContainer.appendChild(button);
            }, index * 100);
        });
        this.showOptions();
    }

    // Actualizar información del jugador
    updatePlayerInfo() {
        if (this.playerData.name) {
            this.playerInfo.textContent = `${this.playerData.name} | Don: ${this.playerData.gift} | HP: ${this.playerData.hp}/${this.playerData.maxHp} | Objetos: ${this.playerData.inventory.length}`;
        } else {
            this.playerInfo.textContent = 'Bienvenido a Solo Legenxs';
        }
    }

    // Efecto de escritura secuencial
    async typeText(texts, delay = 1000) {
        this.hideInterface();

        for (const text of texts) {
            this.addStoryText(text.content, text.className || 'text-story');
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // Manejar entrada de texto
    handleTextInput() {
        const input = this.textInput.value.trim();
        if (!input) return;

        this.addStoryText(`> ${input}`, 'text-user-input');
        this.textInput.value = '';

        // Procesar según el estado del juego
        switch (this.gameState) {
            case 'enter_name':
                this.processPlayerName(input);
                break;
            default:
                this.addStoryText('Comando no reconocido.', 'text-error');
        }
    }

    // Procesar nombre del jugador
    processPlayerName(name) {
        if (name.length < 2) {
            this.addStoryText('El nombre debe tener al menos 2 caracteres.', 'text-error');
            return;
        }

        this.playerData.name = name;
        this.addStoryText(`¡Perfecto! Tu héroe se llamará ${name}.`, 'text-success');
        this.updatePlayerInfo();

        setTimeout(() => {
            this.showGiftSelection();
        }, 1500);
    }

    // Mostrar menú principal
    showMainMenu() {
        this.clearStory();
        this.gameState = 'main_menu';

        this.addStoryText('Menú Principal', 'text-title');
        this.addStoryText('¿Qué deseas hacer?', 'text-info');

        const options = [];

        if (this.playerData.savedGame) {
            options.push({
                text: '🎮 Continuar Aventura',
                action: () => this.showTownMenu()
            });
        } else {
            options.push({
                text: '⚔️ Nueva Aventura',
                action: () => this.startNewGame()
            });
        }

        options.push(
            {
                text: '⚙️ Ajustes',
                action: () => this.showSettings()
            },
            {
                text: '🚪 Salir',
                action: () => this.exitGame()
            }
        );

        this.showOptionsMenu(options);
    }

    // Iniciar nueva partida
    async startNewGame() {
        this.clearStory();
        this.gameState = 'new_game';

        const introTexts = [
            { content: 'Iniciando nueva aventura...', className: 'text-info' },
            { content: 'Bienvenido al mundo de las leyendas,', className: 'text-story' },
            { content: 'donde cada decisión forja tu destino', className: 'text-story' },
            { content: 'y cada batalla te acerca a la gloria.', className: 'text-story' },
            { content: 'Prepárate para una experiencia épica...', className: 'text-story' }
        ];

        await this.typeText(introTexts, 1200);

        setTimeout(() => {
            this.requestPlayerName();
        }, 1000);
    }

    // Solicitar nombre del jugador
    requestPlayerName() {
        this.clearStory();
        this.gameState = 'enter_name';

        this.addStoryText('Creación de Personaje', 'text-title');
        this.addStoryText('Antes de comenzar tu aventura, necesitas un nombre que inspire respeto y temor en tus enemigos.', 'text-info');
        this.addStoryText('¿Cómo se llamará tu héroe?', 'text-info');

        this.showTextInput('Introduce el nombre de tu héroe...');
    }

    // Mostrar selección de don
    showGiftSelection() {
        this.clearStory();
        this.gameState = 'gift_selection';

        this.addStoryText('Selección de Don', 'text-title');
        this.addStoryText('Los antiguos dioses te han bendecido con la oportunidad de elegir un don especial.', 'text-info');
        this.addStoryText('Este don te acompañará durante toda tu aventura y definirá tu estilo de combate.', 'text-info');

        const options = [
            {
                text: '🛡️ Don de Resistencia',
                action: () => this.selectGift('Resistencia')
            },
            {
                text: '⚡ Don de Velocidad',
                action: () => this.selectGift('Velocidad')
            },
            {
                text: '🔮 Don de Magia',
                action: () => this.selectGift('Magia')
            },
            {
                text: '📖 Ver Descripciones',
                action: () => this.showGiftDescriptions()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Mostrar descripciones de los dones
    showGiftDescriptions() {
        this.clearStory();
        this.gameState = 'gift_descriptions';

        this.addStoryText('Descripciones de los Dones', 'text-title');
        this.addStoryText('Cada don otorga habilidades únicas que influirán en tu aventura:', 'text-info');

        this.addStoryText('🛡️ Don de Resistencia:', 'text-warning');
        this.addStoryText('Aumenta tu resistencia física y mental. Los héroes con este don pueden soportar más daño y resistir efectos negativos. Ideal para guerreros que prefieren el combate directo.', 'text-story');

        this.addStoryText('⚡ Don de Velocidad:', 'text-warning');
        this.addStoryText('Mejora tu agilidad y reflejos. Permite esquivar ataques más fácilmente y actuar primero en combate. Perfecto para exploradores y ladrones.', 'text-story');

        this.addStoryText('🔮 Don de Magia:', 'text-warning');
        this.addStoryText('Desbloquea el poder arcano que fluye por el mundo. Permite lanzar hechizos poderosos y comprender secretos místicos. Esencial para magos y eruditos.', 'text-story');

        const options = [
            {
                text: '↩️ Volver a la Selección',
                action: () => this.showGiftSelection()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Seleccionar don
    selectGift(gift) {
        this.playerData.gift = gift;
        this.playerData.savedGame = true;

        this.clearStory();
        this.addStoryText(`¡Has elegido el Don de ${gift}!`, 'text-success');
        this.addStoryText(`Los dioses sonríen ante tu elección. El poder del ${gift} fluye ahora por tus venas.`, 'text-story');
        this.addStoryText('Tu personaje ha sido creado exitosamente.', 'text-info');

        this.updatePlayerInfo();

        setTimeout(() => {
            this.showTownMenu();
        }, 2500);
    }

    // Mostrar menú del pueblo
    showTownMenu() {
        this.clearStory();
        this.gameState = 'town_menu';

        this.addStoryText(`El Pueblo - ${this.playerData.name}`, 'text-title');
        this.addStoryText('Te encuentras en el corazón del pueblo, donde aventureros de todo el reino se reúnen para planear sus próximas hazañas.', 'text-info');
        this.addStoryText('¿Qué deseas hacer?', 'text-info');

        const options = [
            {
                text: '⚔️ Ir de Raid',
                action: () => this.showPortalsMenu()
            },
            {
                text: '🎒 Inventario',
                action: () => this.showInventory()
            },
            {
                text: '👤 Personaje',
                action: () => this.showCharacterInfo()
            },
            {
                text: '🏪 Tiendas',
                action: () => this.showShops()
            },
            {
                text: '💾 Guardar Partida',
                action: () => this.saveGame()
            },
            {
                text: '🏠 Menú Principal',
                action: () => this.showMainMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Mostrar menú de portales
    async showPortalsMenu() {
        this.clearStory();
        this.gameState = 'portals_menu';

        if (this.firstTimePortals) {
            const introTexts = [
                { content: 'Los Portales Mágicos', className: 'text-title' },
                { content: 'Te encuentras ante un claro ancestral, donde la energía mágica es palpable en el aire.', className: 'text-story' },
                { content: 'Frente a ti se alzan varios portales de distintos tamaños y colores, pulsando con poder arcano.', className: 'text-story' },
                { content: 'Estos son los legendarios Portales Mágicos, conductos a otras dimensiones y realidades.', className: 'text-story' },
                { content: 'Se dice que aventurarse en ellos puede llevar a grandes tesoros... o a un peligro inmenso.', className: 'text-story' },
                { content: 'Al derrotar a los monstruos, sus cuerpos sueltan materiales valiosos que los humanos pueden usar para vender a los comerciantes o forjar piezas de equipamiento que van desde usuales hasta impresionantes piezas legendarias con poderes impensables.', className: 'text-story' }
            ];

            await this.typeText(introTexts, 1500);
            this.firstTimePortals = false;
        } else {
            this.addStoryText('Los Portales Mágicos', 'text-title');
            this.addStoryText('Los portales brillan con energía mística, esperando tu decisión.', 'text-info');
        }

        const options = [
            {
                text: '🌀 Entrar al Portal Principal',
                action: () => this.enterPortal()
            },
            {
                text: '🎒 Revisar Inventario',
                action: () => this.showInventory()
            },
            {
                text: '🏘️ Volver al Pueblo',
                action: () => this.showTownMenu()
            },
            {
                text: '📜 Leer Historia de los Portales',
                action: () => this.showPortalHistory()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Métodos de funcionalidades (próximamente)
    showInventory() {
        this.clearStory();
        this.addStoryText('Inventario', 'text-title');
        this.addStoryText('Tu inventario está vacío por ahora. Esta funcionalidad estará disponible próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showTownMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showCharacterInfo() {
        this.clearStory();
        this.addStoryText('Información del Personaje', 'text-title');
        this.addStoryText(`Nombre: ${this.playerData.name}`, 'text-info');
        this.addStoryText(`Don: ${this.playerData.gift}`, 'text-info');
        this.addStoryText(`Nivel: ${this.playerData.level}`, 'text-info');
        this.addStoryText('Más estadísticas estarán disponibles próximamente.', 'text-story');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showTownMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showShops() {
        this.clearStory();
        this.addStoryText('Tiendas', 'text-title');
        this.addStoryText('Las tiendas del pueblo estarán disponibles próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showTownMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showSettings() {
        this.clearStory();
        this.addStoryText('Ajustes', 'text-title');
        this.addStoryText('Los ajustes del juego estarán disponibles próximamente.', 'text-info');

        const options = [
            {
                text: '↩️ Volver',
                action: () => this.showMainMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    enterPortal() {
        this.clearStory();
        this.gameState = 'portal_selection';

        this.addStoryText('Selección de Portal', 'text-title');
        this.addStoryText('Ante ti se extienden varios portales, cada uno pulsando con una energía única y ominosa.', 'text-info');
        this.addStoryText('Cada portal alberga criaturas más terribles que el anterior. Elige sabiamente tu destino...', 'text-warning');

        const options = [
            {
                text: '🟢 Portal Verde - Bosques Encantados',
                action: () => this.showPortalDescription('verde')
            },
            {
                text: '🟤 Portal Marrón - Cavernas Rocosas',
                action: () => this.showPortalDescription('marron')
            },
            {
                text: '🔵 Portal Azul - Profundidades Abisales',
                action: () => this.showPortalDescription('azul')
            },
            {
                text: '🟡 Portal Amarillo - Desiertos Ardientes',
                action: () => this.showPortalDescription('amarillo')
            },
            {
                text: '🔴 Portal Rojo - Infiernos Carmesí',
                action: () => this.showPortalDescription('rojo')
            },
            {
                text: '🌟 Portal Místico - Reino de las Sombras',
                action: () => this.showPortalDescription('mistico')
            },
            {
                text: '↩️ Volver a los Portales',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    showPortalDescription(portalType) {
        this.clearStory();
        this.gameState = 'portal_description';

        const portalData = {
            verde: {
                title: '🟢 Portal Verde - Bosques Encantados',
                difficulty: 'Dificultad: ⭐ Principiante',
                description: 'Un portal que brilla con una suave luz esmeralda. A través de él se vislumbran antiguos bosques donde la magia natural aún fluye.',
                monsters: 'Monstruos: Lobos sombríos, Arañas gigantes, Goblins salvajes y Ents corruptos. Criaturas del bosque que han sido tocadas por la magia oscura.',
                rewards: 'Recompensas: Maderas mágicas, pieles resistentes, hierbas curativas y gemas verdes básicas.'
            },
            marron: {
                title: '🟤 Portal Marrón - Cavernas Rocosas',
                difficulty: 'Dificultad: ⭐⭐ Intermedio',
                description: 'Un portal de tonos terrosos que emana el eco de profundas cavernas. El aire que emerge huele a tierra húmeda y metal.',
                monsters: 'Monstruos: Trolls de piedra, Murciélagos vampiro gigantes, Escorpiones de roca y Golems menores. Bestias que han evolucionado en la oscuridad subterránea.',
                rewards: 'Recompensas: Minerales preciosos, cristales de tierra, armaduras de piedra y gemas marrones resistentes.'
            },
            azul: {
                title: '🔵 Portal Azul - Profundidades Abisales',
                difficulty: 'Dificultad: ⭐⭐⭐ Avanzado',
                description: 'Un portal que ondula como agua profunda, emanando una fría brisa marina. Se escuchan ecos de criaturas marinas ancestrales.',
                monsters: 'Monstruos: Krakens juveniles, Sirenas corrompidas, Tiburones espectrales y Leviatanes menores. Horrores de las profundidades que nunca han visto la luz.',
                rewards: 'Recompensas: Escamas de dragón marino, perlas abisales, armas de coral encantado y gemas azules de poder acuático.'
            },
            amarillo: {
                title: '🟡 Portal Amarillo - Desiertos Ardientes',
                difficulty: 'Dificultad: ⭐⭐⭐⭐ Experto',
                description: 'Un portal que irradia calor intenso y luz dorada cegadora. Arena ardiente se filtra constantemente desde sus bordes.',
                monsters: 'Monstruos: Dragones de arena, Escorpiones de fuego gigantes, Djinns malevolentes y Fénix sombríos. Criaturas forjadas en el calor eterno del desierto.',
                rewards: 'Recompensas: Cristales de fuego puro, arena temporal, armaduras de escamas doradas y gemas amarillas de poder solar.'
            },
            rojo: {
                title: '🔴 Portal Rojo - Infiernos Carmesí',
                difficulty: 'Dificultad: ⭐⭐⭐⭐⭐ Maestro',
                description: 'Un portal que arde con llamas eternas, emanando un calor infernal. Se escuchan los gritos de almas atormentadas y el rugido de demonios.',
                monsters: 'Monstruos: Demonios mayores, Dragones infernales, Balrogs menores y Archiduques del infierno. Las criaturas más temibles que han emergido del averno.',
                rewards: 'Recompensas: Metales infernales, llamas eternas, armaduras demoníacas y gemas rojas de poder diabólico.'
            },
            mistico: {
                title: '🌟 Portal Místico - Reino de las Sombras',
                difficulty: 'Dificultad: ⭐⭐⭐⭐⭐⭐ Legendario',
                description: 'Un portal que parece absorber la luz misma, rodeado de energías arcanas incomprensibles. La realidad se distorsiona a su alrededor.',
                monsters: 'Monstruos: Señores de las Sombras, Dragones Cósmicos, Entidades del Vacío y Dioses Caídos. Seres que trascienden la comprensión mortal y cuyo poder desafía las leyes de la realidad.',
                rewards: 'Recompensas: Fragmentos de realidad, esencias cósmicas, artefactos legendarios y gemas místicas con poderes impensables.'
            }
        };

        const portal = portalData[portalType];

        this.addStoryText(portal.title, 'text-title');
        this.addStoryText(portal.difficulty, 'text-warning');
        this.addStoryText(portal.description, 'text-info');
        this.addStoryText(portal.monsters, 'text-story');
        this.addStoryText(portal.rewards, 'text-success');

        const options = [
            {
                text: '⚔️ Entrar al Portal (Próximamente)',
                action: () => this.enterSpecificPortal(portalType)
            },
            {
                text: '↩️ Volver a Selección de Portales',
                action: () => this.enterPortal()
            }
        ];

        this.showOptionsMenu(options);
    }

    enterSpecificPortal(portalType) {
        this.currentPortal = portalType;

        if (portalType === 'verde') {
            this.startCombat();
        } else {
            this.clearStory();
            this.addStoryText('Preparándose para la Aventura...', 'text-title');
            this.addStoryText('El sistema de combate y exploración estará disponible próximamente.', 'text-info');
            this.addStoryText(`Has seleccionado el portal ${portalType}. ¡Prepárate para enfrentar grandes desafíos!`, 'text-warning');

            const options = [
                {
                    text: '↩️ Volver a Selección de Portales',
                    action: () => this.enterPortal()
                }
            ];

            this.showOptionsMenu(options);
        }
    }

    // Sistema de combate
    startCombat() {
        this.clearStory();
        this.gameState = 'combat';

        // Seleccionar enemigo aleatorio del portal verde
        const monsters = this.portalMonsters[this.currentPortal];
        const randomMonster = monsters[Math.floor(Math.random() * monsters.length)];

        // Crear copia del enemigo para no modificar el original
        this.currentEnemy = {
            ...randomMonster,
            hp: randomMonster.maxHp,
            loot: [...randomMonster.loot]
        };

        this.addStoryText('¡Encuentro Enemigo!', 'text-title');
        this.addStoryText(`Un ${this.currentEnemy.name} aparece ante ti.`, 'text-warning');
        this.addStoryText(this.currentEnemy.description, 'text-story');
        this.addStoryText(`HP: ${this.currentEnemy.hp}/${this.currentEnemy.maxHp}`, 'text-info');

        this.showCombatOptions();
    }

    showCombatOptions() {
        const options = [
            {
                text: '⚔️ Atacar',
                action: () => this.playerAttack()
            },
            {
                text: '🛡️ Defenderse',
                action: () => this.playerDefend()
            },
            {
                text: '🏃 Huir',
                action: () => this.playerFlee()
            }
        ];

        this.showOptionsMenu(options);
    }

    playerAttack() {
        this.clearStory();
        this.addStoryText('Combate', 'text-title');

        // Calcular daño del jugador
        const baseDamage = this.playerData.attack;
        const randomFactor = 0.8 + Math.random() * 0.4; // 80% - 120% del daño base
        const damage = Math.floor(baseDamage * randomFactor);
        const finalDamage = Math.max(1, damage - this.currentEnemy.defense);

        this.currentEnemy.hp -= finalDamage;
        this.addStoryText(`Atacas al ${this.currentEnemy.name} y le infliges ${finalDamage} puntos de daño.`, 'text-info');

        if (this.currentEnemy.hp <= 0) {
            this.currentEnemy.hp = 0;
            this.addStoryText(`¡Has derrotado al ${this.currentEnemy.name}!`, 'text-success');
            this.showVictoryScreen();
            return;
        }

        this.addStoryText(`${this.currentEnemy.name} HP: ${this.currentEnemy.hp}/${this.currentEnemy.maxHp}`, 'text-warning');

        // Turno del enemigo
        setTimeout(() => {
            this.enemyAttack();
        }, 1500);
    }

    enemyAttack() {
        const baseDamage = this.currentEnemy.attack;
        const randomFactor = 0.8 + Math.random() * 0.4;
        const damage = Math.floor(baseDamage * randomFactor);
        const finalDamage = Math.max(1, damage - this.playerData.defense);

        this.playerData.hp -= finalDamage;
        this.addStoryText(`${this.currentEnemy.name} te ataca y te inflige ${finalDamage} puntos de daño.`, 'text-error');

        if (this.playerData.hp <= 0) {
            this.playerData.hp = 0;
            this.addStoryText('¡Has sido derrotado!', 'text-error');
            this.showDefeatScreen();
            return;
        }

        this.addStoryText(`Tu HP: ${this.playerData.hp}/${this.playerData.maxHp}`, 'text-info');
        this.updatePlayerInfo();

        setTimeout(() => {
            this.showCombatOptions();
        }, 1500);
    }

    playerDefend() {
        this.clearStory();
        this.addStoryText('Combate', 'text-title');
        this.addStoryText('Te preparas para defenderte del próximo ataque.', 'text-info');

        // Turno del enemigo con daño reducido
        setTimeout(() => {
            const baseDamage = this.currentEnemy.attack;
            const randomFactor = 0.8 + Math.random() * 0.4;
            const damage = Math.floor(baseDamage * randomFactor);
            const reducedDamage = Math.floor(damage * 0.5); // 50% de reducción por defenderse
            const finalDamage = Math.max(1, reducedDamage - this.playerData.defense);

            this.playerData.hp -= finalDamage;
            this.addStoryText(`${this.currentEnemy.name} te ataca, pero tu defensa reduce el daño a ${finalDamage} puntos.`, 'text-warning');

            if (this.playerData.hp <= 0) {
                this.playerData.hp = 0;
                this.addStoryText('¡Has sido derrotado!', 'text-error');
                this.showDefeatScreen();
                return;
            }

            this.addStoryText(`Tu HP: ${this.playerData.hp}/${this.playerData.maxHp}`, 'text-info');
            this.updatePlayerInfo();

            setTimeout(() => {
                this.showCombatOptions();
            }, 1500);
        }, 1500);
    }

    playerFlee() {
        this.clearStory();
        this.addStoryText('Huida', 'text-title');

        const fleeChance = Math.random();
        if (fleeChance > 0.3) { // 70% de probabilidad de huir
            this.addStoryText('¡Has logrado escapar del combate!', 'text-success');

            const options = [
                {
                    text: '↩️ Volver a los Portales',
                    action: () => this.showPortalsMenu()
                }
            ];

            this.showOptionsMenu(options);
        } else {
            this.addStoryText('¡No puedes escapar!', 'text-error');

            setTimeout(() => {
                this.enemyAttack();
            }, 1500);
        }
    }

    showVictoryScreen() {
        this.clearStory();
        this.gameState = 'victory';

        this.addStoryText('¡Victoria!', 'text-title');
        this.addStoryText(`Has derrotado al ${this.currentEnemy.name}.`, 'text-success');
        this.addStoryText('El cuerpo del enemigo yace inmóvil ante ti.', 'text-story');

        const options = [
            {
                text: '🔍 Rebuscar objetos en el cuerpo',
                action: () => this.searchLoot()
            },
            {
                text: '↩️ Continuar explorando',
                action: () => this.startCombat()
            },
            {
                text: '🏠 Volver a los Portales',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    searchLoot() {
        this.clearStory();
        this.gameState = 'looting';

        this.addStoryText('Rebuscando en el cuerpo...', 'text-title');
        this.addStoryText(`Examinas cuidadosamente el cuerpo del ${this.currentEnemy.name}.`, 'text-info');

        // Generar botín aleatorio
        const lootFound = this.generateRandomLoot();

        if (lootFound) {
            const rarity = this.rarities[lootFound.rarity];
            this.addStoryText(`¡Has encontrado algo!`, 'text-success');
            this.addStoryText(`${lootFound.name} (${rarity.name})`, rarity.color);
            this.addStoryText(lootFound.description, 'text-story');

            // Agregar al inventario
            this.playerData.inventory.push({
                ...lootFound,
                obtainedFrom: this.currentEnemy.name,
                timestamp: new Date().toLocaleString()
            });

        } else {
            this.addStoryText('No encuentras nada útil en el cuerpo.', 'text-warning');
            this.addStoryText('A veces la suerte no está de tu lado.', 'text-story');
        }

        const options = [
            {
                text: '⚔️ Buscar otro enemigo',
                action: () => this.startCombat()
            },
            {
                text: '🎒 Ver Inventario',
                action: () => this.showInventory()
            },
            {
                text: '🏠 Volver a los Portales',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    generateRandomLoot() {
        // 70% de probabilidad de encontrar algo
        if (Math.random() > 0.7) {
            return null;
        }

        // Determinar rareza basada en probabilidades
        const randomValue = Math.random() * 100;
        let selectedRarity = 'common';
        let cumulativeChance = 0;

        for (const [rarity, data] of Object.entries(this.rarities)) {
            cumulativeChance += data.chance;
            if (randomValue <= cumulativeChance) {
                selectedRarity = rarity;
                break;
            }
        }

        // Filtrar objetos por rareza
        const availableItems = this.currentEnemy.loot.filter(item => item.rarity === selectedRarity);

        if (availableItems.length === 0) {
            // Si no hay objetos de esa rareza, usar común
            const commonItems = this.currentEnemy.loot.filter(item => item.rarity === 'common');
            return commonItems[Math.floor(Math.random() * commonItems.length)];
        }

        return availableItems[Math.floor(Math.random() * availableItems.length)];
    }

    showDefeatScreen() {
        this.clearStory();
        this.gameState = 'defeat';

        this.addStoryText('Derrota', 'text-title');
        this.addStoryText('Has caído en combate...', 'text-error');
        this.addStoryText('Tu visión se desvanece mientras la oscuridad te envuelve.', 'text-story');
        this.addStoryText('Pero esto no es el final de tu historia.', 'text-info');

        // Restaurar HP para poder continuar
        this.playerData.hp = this.playerData.maxHp;
        this.updatePlayerInfo();

        const options = [
            {
                text: '🔄 Intentar de nuevo',
                action: () => this.startCombat()
            },
            {
                text: '🏠 Volver a los Portales',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    // Actualizar la función showInventory para mostrar el inventario real
    showInventory() {
        this.clearStory();
        this.addStoryText('Inventario', 'text-title');

        if (this.playerData.inventory.length === 0) {
            this.addStoryText('Tu inventario está vacío.', 'text-info');
            this.addStoryText('Derrota enemigos para obtener objetos valiosos.', 'text-story');
        } else {
            this.addStoryText(`Objetos encontrados: ${this.playerData.inventory.length}`, 'text-info');

            // Agrupar por rareza
            const itemsByRarity = {};
            this.playerData.inventory.forEach(item => {
                if (!itemsByRarity[item.rarity]) {
                    itemsByRarity[item.rarity] = [];
                }
                itemsByRarity[item.rarity].push(item);
            });

            // Mostrar objetos agrupados por rareza
            Object.entries(itemsByRarity).forEach(([rarity, items]) => {
                const rarityData = this.rarities[rarity];
                this.addStoryText(`\n--- ${rarityData.name} ---`, rarityData.color);
                items.forEach(item => {
                    this.addStoryText(`• ${item.name} (de ${item.obtainedFrom})`, 'text-story');
                });
            });
        }

        const options = [
            {
                text: '↩️ Volver',
                action: () => {
                    if (this.gameState === 'looting') {
                        this.searchLoot();
                    } else {
                        this.showTownMenu();
                    }
                }
            }
        ];

        this.showOptionsMenu(options);
    }

    async showPortalHistory() {
        this.clearStory();
        this.gameState = 'portal_history';

        const historyTexts = [
            { content: 'Historia de los Portales Mágicos', className: 'text-title' },
            { content: 'Te encuentras ante un claro ancestral, donde la energía mágica es palpable en el aire.', className: 'text-story' },
            { content: 'Frente a ti se alzan varios portales de distintos tamaños y colores, pulsando con poder arcano.', className: 'text-story' },
            { content: 'Estos son los legendarios Portales Mágicos, conductos a otras dimensiones y realidades.', className: 'text-story' },
            { content: 'Se dice que aventurarse en ellos puede llevar a grandes tesoros... o a un peligro inmenso.', className: 'text-story' },
            { content: 'Al derrotar a los monstruos, sus cuerpos sueltan materiales valiosos que los humanos pueden usar para vender a los comerciantes o forjar piezas de equipamiento que van desde usuales hasta impresionantes piezas legendarias con poderes impensables.', className: 'text-story' }
        ];

        await this.typeText(historyTexts, 1500);

        const options = [
            {
                text: '↩️ Volver a los Portales',
                action: () => this.showPortalsMenu()
            }
        ];

        this.showOptionsMenu(options);
    }

    saveGame() {
        this.playerData.savedGame = true;
        this.addStoryText('¡Partida guardada exitosamente!', 'text-success');

        setTimeout(() => {
            this.showTownMenu();
        }, 1500);
    }

    exitGame() {
        if (confirm('¿Estás seguro de que quieres salir del juego?')) {
            this.clearStory();
            this.addStoryText('¡Gracias por jugar Solo Legenxs!', 'text-success');
            this.addStoryText('Tu aventura te espera cuando regreses.', 'text-story');
            this.hideInterface();
        } else {
            this.showMainMenu();
        }
    }
}

// Inicializar el juego cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    const game = new SoloLegenxsGame();
});