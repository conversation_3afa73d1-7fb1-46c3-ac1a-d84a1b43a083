import tkinter as tk
from tkinter import simpledialog, scrolledtext, font

class GameConsole:
    def __init__(self, master):
        self.master = master
        master.title("Solo Legenxs - Consola")
        master.geometry("700x500") # Aumentado el tamaño de la ventana

        self.text_area = scrolledtext.ScrolledText(master, wrap=tk.WORD, height=20, width=80, state='disabled', bg='black', fg='lightgrey') # Aumentada la altura
        self.text_area.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Definir tags para colores y estilos
        default_font = font.Font(family="Lucida Console", size=10)
        self.text_area.tag_configure("default", font=default_font)
        self.text_area.tag_configure("title", foreground="cyan", font=(default_font.cget("family"), default_font.cget("size"), "bold underline"))
        self.text_area.tag_configure("info", foreground="white")
        self.text_area.tag_configure("menu_option", foreground="yellow", font=default_font)
        self.text_area.tag_configure("prompt", foreground="lightgreen", font=default_font)
        self.text_area.tag_configure("user_input", foreground="lightblue", font=(default_font.cget("family"), default_font.cget("size"), "italic"))
        self.text_area.tag_configure("error", foreground="red", font=default_font)
        self.text_area.tag_configure("success", foreground="green", font=default_font)
        self.text_area.tag_configure("story", foreground="lightgrey", font=(default_font.cget("family"), default_font.cget("size"), "italic"))

        self.input_entry = tk.Entry(master, width=80, bg='darkgrey', fg='white')
        self.input_entry.pack(padx=10, pady=(0,5), fill=tk.X, expand=False)
        self.input_entry.bind("<Return>", self.process_input_event) # Cambiado para llamar a un manejador de eventos

        self.send_button = tk.Button(master, text="Enviar", command=self.process_input_event, bg='grey', fg='white') # Cambiado para llamar a un manejador de eventos
        self.send_button.pack(padx=10, pady=(0,10), fill=tk.X, expand=False)

        self.estado_juego = "menu_principal"  # Estados: menu_principal, nueva_partida_intro, nombre_personaje, seleccion_don, descripcion_don, pueblo, portales_magicos
        self.nombre_personaje = ""
        self.don_seleccionado = ""
        self.partida_guardada = False
        self.character_data = {}
        self.primera_vez_portales = True # Nueva variable para controlar el mensaje de introducción a los portales
        
        self.mostrar_menu_gui()

    def clear_console(self):
        self.text_area.config(state='normal')
        self.text_area.delete(1.0, tk.END)
        self.text_area.config(state='disabled')

    def print_to_console(self, message, tag=None):
        if not message: # Evitar error si el mensaje está vacío
            return
        self.text_area.config(state='normal')
        # Guardar la posición actual antes de insertar para aplicar el tag solo al nuevo texto
        start_index = self.text_area.index(tk.END + "-1c") 
        self.text_area.insert(tk.END, message + "\n")
        end_index = self.text_area.index(tk.END + "-1c")
        if tag:
            self.text_area.tag_add(tag, start_index, end_index)
        else:
            self.text_area.tag_add("default", start_index, end_index) # Aplicar tag por defecto si no se especifica
        self.text_area.config(state='disabled')
        self.text_area.see(tk.END) # Auto-scroll

    def mostrar_menu_gui(self):
        self.clear_console()
        self.print_to_console("SOLO LEGENXS", "title")
        self.print_to_console("\nMenu Principal:", "info")
        if self.partida_guardada:
            self.print_to_console("1. Continuar aventura", "menu_option")
        else:
            self.print_to_console("1. Comenzar una nueva aventura", "menu_option")
        self.print_to_console("2. Ajustes (Proximamente)", "menu_option")
        self.print_to_console("3. Salir", "menu_option")
        self.print_to_console("\nEscribe el numero de tu eleccion:", "prompt")
        self.estado_juego = "menu_principal"

    def mostrar_opciones_don(self):
        self.clear_console()
        self.print_to_console("Elige un Don para tu Personaje:", "title")
        self.print_to_console("Los Dones otorgan mejoras pasivas y pueden influir en tus decisiones.", "info")
        self.print_to_console("1. Resistencia", "menu_option")
        self.print_to_console("2. Velocidad", "menu_option")
        self.print_to_console("3. Magia", "menu_option")
        self.print_to_console("4. Ver descripcion de los Dones (D)", "menu_option")
        self.print_to_console("\nEscribe el numero o letra de tu eleccion:", "prompt")
        self.estado_juego = "seleccion_don"

    def mostrar_descripcion_dones(self):
        self.clear_console()
        self.print_to_console("Descripcion de los Dones:", "title")
        self.print_to_console("Cada Don ofrece una bonificacion pasiva unica que afecta tus atributos al subir de nivel y puede abrir opciones especiales en ciertos eventos del juego.", "info")
        self.print_to_console("\n- Resistencia: Aumenta tu capacidad de supervivencia, otorgando mas puntos de vida o reduccion de daño.", "story")
        self.print_to_console("- Velocidad: Mejora tu agilidad, permitiendote actuar antes en combate o tener exito en pruebas de destreza.", "story")
        self.print_to_console("- Magia: Potencia tus habilidades arcanas, incrementando el daño de hechizos o desbloqueando conocimientos ocultos.", "story")
        self.print_to_console("\nPresiona Enter para volver a la seleccion de Dones.", "prompt")
        self.estado_juego = "descripcion_don" # Estado para esperar Enter y volver

    def mostrar_menu_pueblo_gui(self):
        self.clear_console()
        self.print_to_console(f"El Pueblo - {self.nombre_personaje}", "title")
        self.print_to_console("\n¿Que deseas hacer?", "info")
        self.print_to_console("1. Ir de raid", "menu_option") # Texto actualizado
        self.print_to_console("2. Abrir inventario (Proximamente)", "menu_option")
        self.print_to_console("3. Personaje (Proximamente)", "menu_option")
        self.print_to_console("4. Tiendas (Proximamente)", "menu_option")
        self.print_to_console("5. Ajustes (Proximamente)", "menu_option")
        self.print_to_console("6. Guardar partida", "menu_option")
        self.print_to_console("7. Salir al menu principal", "menu_option")
        self.print_to_console("\nEscribe el numero de tu eleccion:", "prompt")
        self.estado_juego = "pueblo"

    def mostrar_menu_portales_gui(self, mostrar_intro=False): # Default de mostrar_intro a False
        self.clear_console()
        if mostrar_intro: # Si se debe mostrar la intro
            lineas_intro_portales = [
                "Te encuentras ante un claro ancestral, la energía mágica es palpable.",
                "Frente a ti se alzan varios portales de distintos tamaños y colores.",
                "Estos son 'Los Portales Mágicos', conductos a otras dimensiones y realidades.",
                "Se dice que aventurarse en ellos puede llevar a grandes tesoros... o a un peligro inmenso.",
                "Los monstruos que habitan al otro lado custodian materiales valiosos para mejorar tu equipo."
            ]
            self.print_to_console("--- Los Portales Magicos ---")
            delay_ms = 0
            for i, linea in enumerate(lineas_intro_portales):
                self.master.after(delay_ms, lambda l=linea: self.print_to_console(l))
                delay_ms += 1000 # 1 segundo por línea
            
            self.master.after(delay_ms, lambda: self.print_to_console("\nPresiona Enter para continuar..."))
            self.estado_juego = "portales_intro_espera" # Siempre esperar Enter si se muestra la intro
            return # Salimos para que no se muestre el menú normal inmediatamente

        self.print_to_console("--- Los Portales Magicos ---")
        self.print_to_console("1. Entrar en el portal principal")
        self.print_to_console("2. Inventario")
        self.print_to_console("3. Volver a El Pueblo")
        self.print_to_console("4. Leer historia de los portales (D)")
        self.print_to_console("---------------------------")
        self.print_to_console("Elige una opción:")

    # Wrapper para el evento de entrada, para que coincida con la firma de bind
    def process_input_event(self, event=None): 
        self.process_input()

    def process_input(self): # Eliminado el parámetro 'event' de aquí
        user_input = self.input_entry.get().strip()
        opcion = user_input.lower() # Definir opcion aquí
        self.input_entry.delete(0, tk.END)
        self.print_to_console(f"> {user_input}", "user_input") # Mostrar la entrada del usuario

        if self.estado_juego == "menu_principal":
            if opcion == "1": # Usar opcion
                if self.partida_guardada:
                    self.clear_console()
                    self.print_to_console("Continuando aventura... (Proximamente)", "info")
                    # Aquí podrías cargar el estado del juego guardado
                    self.master.after(2000, self.mostrar_menu_pueblo_gui) # Ir al pueblo si hay partida
                else:
                    self.estado_juego = "nueva_partida_intro"
                    self.clear_console()
                    self.print_to_console("Iniciando nueva aventura...", "info")
                    self.master.after(500, lambda: self.print_to_console("Bienvenido al juego de las leyendas,", "story"))
                    self.master.after(1500, lambda: self.print_to_console("este juego sera recordado por su dificultad", "story"))
                    self.master.after(2500, lambda: self.print_to_console("y por ser tremendamente adictivo,", "story"))
                    self.master.after(3500, lambda: self.print_to_console("sin mas dilacion continuemos...", "story"))
                    self.master.after(4500, self.solicitar_nombre_personaje)
            elif opcion == "2": # Usar opcion
                self.clear_console()
                self.print_to_console("Ajustes del juego (Proximamente)...", "info")
                self.master.after(2000, self.mostrar_menu_gui)
            elif opcion == "3": # Usar opcion
                self.master.quit()
            else:
                self.print_to_console("Opcion no valida. Intentalo de nuevo.", "error")
                self.master.after(1500, self.mostrar_menu_gui)

        elif self.estado_juego == "nombre_personaje": # Este estado se maneja por el diálogo emergente
            # La lógica de solicitar_nombre_personaje se encarga de la transición
            pass # No se necesita lógica aquí ya que el diálogo maneja la entrada

        elif self.estado_juego == "seleccion_don":
            self.don_seleccionado = "" # Reiniciar por si acaso
            # opcion ya está en minúsculas y sin espacios extra
            if opcion in ["1", "resistencia"]:
                self.don_seleccionado = "Resistencia"
            elif opcion in ["2", "velocidad"]:
                self.don_seleccionado = "Velocidad"
            elif opcion in ["3", "magia"]:
                self.don_seleccionado = "Magia"
            elif opcion in ["4", "d"]:
                self.mostrar_descripcion_dones()
                return # Salir para no procesar más hasta que se vuelva de la descripción
            else:
                self.print_to_console("Opcion no valida. Elige un Don de la lista o 'D' para descripcion.", "error")
                self.master.after(2000, self.mostrar_opciones_don)
                return

            if self.don_seleccionado: # Solo si se seleccionó un don válido (no descripción)
                self.character_data['don'] = self.don_seleccionado
                self.print_to_console(f"Has elegido el Don de {self.don_seleccionado}. ¡Sabia eleccion!", "success")
                self.partida_guardada = True # Marcar que hay una partida (personaje creado)
                self.print_to_console("Personaje creado y Don seleccionado.", "info") # Mensaje actualizado
                self.master.after(2000, self.mostrar_menu_pueblo_gui) # Transicion al pueblo
            # Si no se seleccionó un don (ej. opción inválida ya manejada), no hacer nada más aquí

        elif self.estado_juego == "descripcion_don":
            # Cualquier entrada (Enter) nos devuelve a la selección de dones
            self.mostrar_opciones_don()

        elif self.estado_juego == "pueblo":
            if opcion == "1": # Ir de raid
                self.estado_juego = "portales_magicos"
                # Mostrar intro solo si es la primera vez
                self.mostrar_menu_portales_gui(mostrar_intro=self.primera_vez_portales)
            elif opcion == "2":
                self.print_to_console("Funcionalidad 'Abrir inventario' proximamente...", "info")
                self.master.after(1500, self.mostrar_menu_pueblo_gui)
            elif opcion == "3":
                self.print_to_console("Funcionalidad 'Personaje' proximamente...", "info")
                self.master.after(1500, self.mostrar_menu_pueblo_gui)
            elif opcion == "4":
                self.print_to_console("Funcionalidad 'Tiendas' proximamente...", "info")
                self.master.after(1500, self.mostrar_menu_pueblo_gui)
            elif opcion == "5":
                self.print_to_console("Funcionalidad 'Ajustes' proximamente...", "info")
                self.master.after(1500, self.mostrar_menu_pueblo_gui)
            elif opcion == "6":
                # Simular guardado. En un juego real, aquí se guardaría el estado.
                self.partida_guardada = True 
                self.print_to_console("Partida guardada exitosamente.", "success")
                self.master.after(1500, self.mostrar_menu_pueblo_gui)
            elif opcion == "7":
                self.print_to_console("Volviendo al menu principal...", "info")
                self.master.after(1500, self.mostrar_menu_gui)
            else:
                self.print_to_console("Opcion no valida. Intentalo de nuevo.", "error")
                self.master.after(1500, self.mostrar_menu_pueblo_gui)
        
        elif self.estado_juego == "portales_intro_espera":
            # Después de cualquier entrada (Enter), mostramos el menú normal
            # Solo marcamos primera_vez_portales como False si realmente era la primera vez
            if self.primera_vez_portales:
                self.primera_vez_portales = False
            self.mostrar_menu_portales_gui(mostrar_intro=False)
        
        elif self.estado_juego == "portales_magicos":
            if opcion == "1":
                self.print_to_console("Funcionalidad 'Entrar en el portal principal' proximamente...", "info")
                self.master.after(1500, lambda: self.mostrar_menu_portales_gui(mostrar_intro=False)) # Asegurar que no se muestre la intro de nuevo
            elif opcion == "2":
                self.print_to_console("Funcionalidad 'Abrir inventario' (desde portales) proximamente...", "info")
                self.master.after(1500, lambda: self.mostrar_menu_portales_gui(mostrar_intro=False)) # Asegurar que no se muestre la intro de nuevo
            elif opcion == "3":
                self.mostrar_menu_pueblo_gui()
            elif opcion in ["4", "d"]:
                self.mostrar_menu_portales_gui(mostrar_intro=True) # Mostrar la intro de nuevo
                return # Salir para no procesar más hasta que se vuelva de la descripción
            else:
                self.print_to_console("Opcion no valida. Intentalo de nuevo.", "error")
                self.master.after(1500, lambda: self.mostrar_menu_portales_gui(mostrar_intro=False)) # Asegurar que no se muestre la intro de nuevo

    def solicitar_nombre_personaje(self):
        # Limpiar consola antes de mostrar el diálogo para que no se solape con texto anterior
        # self.clear_console() # Opcional, depende de si quieres limpiar antes del popup
        self.print_to_console("A continuacion, elige un nombre para tu heroe.", "prompt")
        # Usar simpledialog para un popup que solicite el nombre
        nombre = simpledialog.askstring("Nombre del Personaje", "Introduce el nombre de tu personaje:", parent=self.master)
        if nombre: # Si el usuario introduce un nombre y no cancela
            self.nombre_personaje = nombre
            self.character_data['nombre'] = self.nombre_personaje
            self.print_to_console(f"Tu personaje se llama: {self.nombre_personaje}", "success")
            self.master.after(1000, self.mostrar_opciones_don) # Ir a la selección de Don
        else: # Si el usuario cancela o cierra el dialogo
            self.print_to_console("Creacion de personaje cancelada. Volviendo al menu principal.", "info")
            self.master.after(2000, self.mostrar_menu_gui)

def main_gui():
    root = tk.Tk()
    console_app = GameConsole(root)
    root.mainloop()

if __name__ == "__main__":
    main_gui()