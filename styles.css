/* Estilos generales */
body {
    font-family: 'Lucida <PERSON>', Monaco, monospace;
    background-color: #222;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: #ddd;
}

.game-container {
    width: 80%;
    max-width: 800px;
    height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* Contenedor de la consola */
.console-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #000;
    border-radius: 5px;
    overflow: hidden;
}

/* Área de texto */
.text-area {
    flex-grow: 1;
    padding: 15px;
    overflow-y: auto;
    background-color: #000;
    color: #ddd;
    font-size: 14px;
    line-height: 1.5;
}

/* Contenedor de entrada */
.input-container {
    display: flex;
    padding: 10px;
    background-color: #111;
    border-top: 1px solid #333;
}

/* Campo de entrada */
.input-entry {
    flex-grow: 1;
    padding: 8px 12px;
    background-color: #333;
    border: none;
    color: #fff;
    font-family: 'Lucida Con<PERSON>e', Monaco, monospace;
    font-size: 14px;
    border-radius: 3px;
    margin-right: 10px;
}

.input-entry:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.5);
}

/* Botón de enviar */
.send-button {
    padding: 8px 15px;
    background-color: #444;
    color: #fff;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-family: 'Lucida Console', Monaco, monospace;
    transition: background-color 0.2s;
}

.send-button:hover {
    background-color: #555;
}

/* Estilos para los diferentes tipos de texto */
.title {
    color: cyan;
    font-weight: bold;
    text-decoration: underline;
    margin-bottom: 10px;
}

.info {
    color: white;
}

.menu-option {
    color: yellow;
    margin-left: 10px;
}

.prompt {
    color: lightgreen;
    margin-top: 10px;
}

.user-input {
    color: lightblue;
    font-style: italic;
}

.error {
    color: red;
}

.success {
    color: green;
}

.story {
    color: lightgrey;
    font-style: italic;
}

/* Animación de escritura */
@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

.cursor {
    display: inline-block;
    width: 8px;
    height: 14px;
    background-color: #ddd;
    margin-left: 2px;
    animation: blink 1s infinite;
    vertical-align: middle;
}