/* Reset y variables CSS */
:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --accent-bg: #2a2a2a;
    --primary-text: #e0e0e0;
    --secondary-text: #b0b0b0;
    --accent-text: #00ff88;
    --warning-text: #ff6b35;
    --error-text: #ff4757;
    --success-text: #2ed573;
    --border-color: #333;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
    color: var(--primary-text);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-x: hidden;
}

/* Contenedor principal */
.game-wrapper {
    width: 100%;
    max-width: 900px;
    height: 90vh;
    min-height: 600px;
}

.game-container {
    background: var(--primary-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Header */
.game-header {
    background: linear-gradient(90deg, var(--secondary-bg) 0%, var(--accent-bg) 100%);
    padding: 20px 30px;
    border-bottom: 2px solid var(--accent-text);
    text-align: center;
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-text);
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
    margin-bottom: 5px;
    letter-spacing: 3px;
}

.game-subtitle {
    font-size: 0.9rem;
    color: var(--secondary-text);
    font-weight: 300;
    letter-spacing: 1px;
}

/* Main content */
.game-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Story display */
.story-display {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: var(--primary-bg);
}

.story-content {
    max-width: 100%;
    line-height: 1.8;
    font-size: 1rem;
}

/* Game interface */
.game-interface {
    background: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    padding: 20px 30px;
    min-height: 120px;
}

/* Options container */
.options-container {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Option buttons */
.option-btn {
    background: linear-gradient(135deg, var(--accent-bg) 0%, var(--secondary-bg) 100%);
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 15px 20px;
    color: var(--primary-text);
    font-family: inherit;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
    position: relative;
    overflow: hidden;
}

.option-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    transition: left 0.5s;
}

.option-btn:hover {
    border-color: var(--accent-text);
    color: var(--accent-text);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.15);
}

.option-btn:hover::before {
    left: 100%;
}

.option-btn:active {
    transform: translateY(0);
}

/* Input container */
.input-container {
    display: flex;
    gap: 15px;
    align-items: center;
}

.text-input {
    flex: 1;
    background: var(--accent-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 15px 20px;
    color: var(--primary-text);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.text-input:focus {
    outline: none;
    border-color: var(--accent-text);
    box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
}

.text-input::placeholder {
    color: var(--secondary-text);
}

.submit-btn {
    background: linear-gradient(135deg, var(--accent-text) 0%, #00cc6a 100%);
    border: none;
    border-radius: 8px;
    padding: 15px 25px;
    color: var(--primary-bg);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

/* Footer */
.game-footer {
    background: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    padding: 15px 30px;
    text-align: center;
}

/* Health Bar */
.health-bar-container {
    margin-bottom: 15px;
    padding: 10px 15px;
    background: var(--accent-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.health-bar-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.healing-timer {
    color: var(--accent-text);
    font-size: 0.8rem;
    animation: pulse 2s infinite;
}

.health-bar-bg {
    position: relative;
    width: 100%;
    height: 20px;
    background: var(--primary-bg);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.health-bar-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #ff4757 0%, #ffa502 50%, #2ed573 100%);
    border-radius: 10px;
    transition: width 0.5s ease-out;
    min-width: 2px;
}

.health-bar-healing {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(46, 213, 115, 0.3) 100%);
    border-radius: 10px;
    transition: width 0.3s ease-out;
    animation: healingGlow 2s infinite;
}

@keyframes healingGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.game-info {
    font-size: 0.9rem;
    color: var(--secondary-text);
}

/* Text styles */
.text-title {
    color: var(--accent-text);
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.text-info {
    color: var(--primary-text);
    margin-bottom: 10px;
}

.text-story {
    color: var(--secondary-text);
    font-style: italic;
    margin-bottom: 10px;
    line-height: 1.6;
}

.text-success {
    color: var(--success-text);
    font-weight: 500;
}

.text-error {
    color: var(--error-text);
    font-weight: 500;
}

.text-warning {
    color: var(--warning-text);
    font-weight: 500;
}

.text-user-input {
    color: #64b5f6;
    font-weight: 500;
    margin-bottom: 10px;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive design */
@media (max-width: 768px) {
    .game-container {
        height: 100vh;
        border-radius: 0;
    }

    .game-title {
        font-size: 2rem;
    }

    .options-container {
        grid-template-columns: 1fr;
    }

    .game-header,
    .game-interface,
    .game-footer {
        padding: 15px 20px;
    }

    .story-display {
        padding: 20px;
    }
}